import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import formidable, { IncomingForm } from 'formidable';
import fs from 'fs';
import path from 'path';

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: '方法不允许' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user?.id) {
      return res.status(401).json({ message: '未授权' });
    }

    const uploadDir = path.join(process.cwd(), 'public', 'uploads');

    // 确保上传目录存在
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    const form = new IncomingForm({
      uploadDir,
      keepExtensions: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB
      filename: (name, ext, part) => {
        // 生成唯一文件名
        const timestamp = Date.now();
        const randomStr = Math.random().toString(36).substring(2, 15);
        return `${timestamp}_${randomStr}${ext}`;
      },
    });

    const [fields, files] = await form.parse(req);

    const file = Array.isArray(files.file) ? files.file[0] : files.file;

    if (!file) {
      return res.status(400).json({ message: '没有文件上传' });
    }

    // 检查文件类型
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'application/pdf', 'text/plain', 'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/zip', 'application/x-zip-compressed'
    ];

    if (!allowedTypes.includes(file.mimetype || '')) {
      // 删除上传的文件
      fs.unlinkSync(file.filepath);
      return res.status(400).json({ message: '不支持的文件类型' });
    }

    const fileName = file.originalFilename || 'unknown';
    const fileUrl = `/uploads/${path.basename(file.filepath)}`;
    const fileSize = file.size;

    return res.status(200).json({
      fileName,
      fileUrl,
      fileSize,
      mimeType: file.mimetype,
    });

  } catch (error) {
    console.error('文件上传失败:', error);
    return res.status(500).json({ message: '文件上传失败' });
  }
}
