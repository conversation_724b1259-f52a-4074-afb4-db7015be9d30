import prisma from './prisma';

// 通知类型枚举
export enum NotificationType {
  // 任务相关
  TASK_ASSIGNED = 'TASK_ASSIGNED',
  TASK_COMPLETED = 'TASK_COMPLETED',
  TASK_STATUS_CHANGED = 'TASK_STATUS_CHANGED',
  TASK_DUE_SOON = 'TASK_DUE_SOON',
  TASK_OVERDUE = 'TASK_OVERDUE',

  // 项目相关
  PROJECT_UPDATED = 'PROJECT_UPDATED',
  PROJECT_STATUS_CHANGED = 'PROJECT_STATUS_CHANGED',
  PROJECT_MILESTONE = 'PROJECT_MILESTONE',
  PROJECT_DUE_SOON = 'PROJECT_DUE_SOON',
  PROJECT_COMPLETED = 'PROJECT_COMPLETED',
  PROJECT_ARCHIVED = 'PROJECT_ARCHIVED',

  // 成员相关
  MEMBER_ADDED = 'MEMBER_ADDED',
  MEMBER_REMOVED = 'MEMBER_REMOVED',
  MEMBER_ROLE_CHANGED = 'MEMBER_ROLE_CHANGED',

  // 文件相关
  FILE_UPLOADED = 'FILE_UPLOADED',
  FILE_SHARED = 'FILE_SHARED',
  FILE_COMMENTED = 'FILE_COMMENTED',

  // 消息相关
  MESSAGE_RECEIVED = 'MESSAGE_RECEIVED',
  MENTION_RECEIVED = 'MENTION_RECEIVED',

  // 系统相关
  USER_APPROVED = 'USER_APPROVED',
  USER_REJECTED = 'USER_REJECTED',
  SYSTEM_MAINTENANCE = 'SYSTEM_MAINTENANCE',
  SYSTEM_UPDATE = 'SYSTEM_UPDATE',
}

// 通知优先级
export enum NotificationPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

export interface CreateNotificationData {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  priority?: NotificationPriority;
  relatedId?: string;
  relatedType?: 'project' | 'task' | 'message' | 'file' | 'user';
  actionUrl?: string; // 点击通知后跳转的URL
  metadata?: Record<string, any>; // 额外的元数据
}

/**
 * 创建通知
 */
export async function createNotification(data: CreateNotificationData) {
  try {
    const notification = await prisma.notification.create({
      data: {
        userId: data.userId,
        type: data.type,
        title: data.title,
        message: data.message,
        relatedId: data.relatedId,
        relatedType: data.relatedType,
      },
    });
    return notification;
  } catch (error) {
    console.error('创建通知失败:', error);
    throw error;
  }
}

/**
 * 创建批量通知（优化版本）
 */
export async function createBulkNotifications(notifications: CreateNotificationData[]) {
  try {
    // 批量创建通知，提高性能
    const result = await prisma.notification.createMany({
      data: notifications.map(notification => ({
        userId: notification.userId,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        relatedId: notification.relatedId,
        relatedType: notification.relatedType,
      })),
      skipDuplicates: true,
    });
    return result;
  } catch (error) {
    console.error('批量创建通知失败:', error);
    throw error;
  }
}

/**
 * 智能通知创建器 - 根据用户偏好和通知历史智能发送
 */
export async function createSmartNotification(data: CreateNotificationData) {
  try {
    // 检查用户是否在短时间内收到过相同类型的通知（防止通知轰炸）
    const recentSimilarNotification = await prisma.notification.findFirst({
      where: {
        userId: data.userId,
        type: data.type,
        relatedId: data.relatedId,
        createdAt: {
          gte: new Date(Date.now() - 5 * 60 * 1000), // 5分钟内
        },
      },
    });

    if (recentSimilarNotification) {
      console.log(`跳过重复通知: ${data.type} for user ${data.userId}`);
      return null;
    }

    // 创建通知
    return await createNotification(data);
  } catch (error) {
    console.error('智能通知创建失败:', error);
    throw error;
  }
}

/**
 * 批量创建通知
 */
export async function createNotifications(notifications: CreateNotificationData[]) {
  try {
    const result = await prisma.notification.createMany({
      data: notifications,
    });
    return result;
  } catch (error) {
    console.error('批量创建通知失败:', error);
    throw error;
  }
}

/**
 * 任务分配通知
 */
export async function notifyTaskAssigned(taskId: string, assigneeId: string, assignerName: string, taskTitle: string, projectTitle: string) {
  return createSmartNotification({
    userId: assigneeId,
    type: NotificationType.TASK_ASSIGNED,
    title: '新任务分配',
    message: `${assignerName} 在项目 "${projectTitle}" 中为您分配了新任务: "${taskTitle}"`,
    priority: NotificationPriority.NORMAL,
    relatedId: taskId,
    relatedType: 'task',
    actionUrl: `/tasks/${taskId}`,
    metadata: {
      assignerName,
      taskTitle,
      projectTitle,
    },
  });
}

/**
 * 任务完成通知
 */
export async function notifyTaskCompleted(taskId: string, projectOwnerId: string, assigneeName: string, taskTitle: string, projectTitle: string) {
  return createSmartNotification({
    userId: projectOwnerId,
    type: NotificationType.TASK_COMPLETED,
    title: '任务已完成',
    message: `${assigneeName} 已完成项目 "${projectTitle}" 中的任务: "${taskTitle}"`,
    priority: NotificationPriority.NORMAL,
    relatedId: taskId,
    relatedType: 'task',
    actionUrl: `/tasks/${taskId}`,
    metadata: {
      assigneeName,
      taskTitle,
      projectTitle,
    },
  });
}

/**
 * 任务状态变更通知
 */
export async function notifyTaskStatusChanged(taskId: string, assigneeIds: string[], changerName: string, taskTitle: string, oldStatus: string, newStatus: string, projectTitle: string) {
  const notifications = assigneeIds.map(assigneeId => ({
    userId: assigneeId,
    type: NotificationType.TASK_STATUS_CHANGED,
    title: '任务状态更新',
    message: `${changerName} 将任务 "${taskTitle}" 的状态从 "${oldStatus}" 更改为 "${newStatus}"`,
    priority: NotificationPriority.NORMAL,
    relatedId: taskId,
    relatedType: 'task' as const,
    actionUrl: `/tasks/${taskId}`,
    metadata: {
      changerName,
      taskTitle,
      oldStatus,
      newStatus,
      projectTitle,
    },
  }));

  return createBulkNotifications(notifications);
}

/**
 * 项目成员添加通知
 */
export async function notifyMemberAdded(projectId: string, newMemberId: string, adderName: string, projectTitle: string) {
  return createSmartNotification({
    userId: newMemberId,
    type: NotificationType.MEMBER_ADDED,
    title: '加入新项目',
    message: `${adderName} 邀请您加入项目 "${projectTitle}"`,
    priority: NotificationPriority.HIGH,
    relatedId: projectId,
    relatedType: 'project',
    actionUrl: `/projects/${projectId}`,
    metadata: {
      adderName,
      projectTitle,
    },
  });
}

/**
 * 项目成员移除通知
 */
export async function notifyMemberRemoved(projectId: string, removedMemberId: string, removerName: string, projectTitle: string) {
  return createSmartNotification({
    userId: removedMemberId,
    type: NotificationType.MEMBER_REMOVED,
    title: '移出项目',
    message: `${removerName} 将您从项目 "${projectTitle}" 中移除`,
    priority: NotificationPriority.HIGH,
    relatedId: projectId,
    relatedType: 'project',
    metadata: {
      removerName,
      projectTitle,
    },
  });
}

/**
 * 项目更新通知（通知所有成员）
 */
export async function notifyProjectUpdated(projectId: string, memberIds: string[], updaterName: string, projectTitle: string, updateType: string) {
  const notifications = memberIds.map(memberId => ({
    userId: memberId,
    type: 'PROJECT_UPDATED' as const,
    title: '项目更新',
    message: `${updaterName} 更新了项目 "${projectTitle}" 的${updateType}`,
    relatedId: projectId,
    relatedType: 'project' as const,
  }));

  return createNotifications(notifications);
}

/**
 * 文件上传通知
 */
export async function notifyFileUploaded(projectId: string, memberIds: string[], uploaderName: string, fileName: string, projectTitle: string) {
  const notifications = memberIds.map(memberId => ({
    userId: memberId,
    type: 'FILE_UPLOADED' as const,
    title: '新文件上传',
    message: `${uploaderName} 在项目 "${projectTitle}" 中上传了文件: "${fileName}"`,
    relatedId: projectId,
    relatedType: 'project' as const,
  }));

  return createNotifications(notifications);
}

/**
 * 获取用户未读通知数量
 */
export async function getUnreadNotificationCount(userId: string) {
  try {
    const count = await prisma.notification.count({
      where: {
        userId,
        read: false,
      },
    });
    return count;
  } catch (error) {
    console.error('获取未读通知数量失败:', error);
    return 0;
  }
}

/**
 * 获取用户通知列表
 */
export async function getUserNotifications(userId: string, limit = 20, offset = 0) {
  try {
    const notifications = await prisma.notification.findMany({
      where: {
        userId,
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: limit,
      skip: offset,
    });
    return notifications;
  } catch (error) {
    console.error('获取用户通知失败:', error);
    return [];
  }
}

/**
 * 标记通知为已读
 */
export async function markNotificationAsRead(notificationId: string, userId: string) {
  try {
    const notification = await prisma.notification.updateMany({
      where: {
        id: notificationId,
        userId,
      },
      data: {
        read: true,
      },
    });
    return notification;
  } catch (error) {
    console.error('标记通知已读失败:', error);
    throw error;
  }
}

/**
 * 标记所有通知为已读
 */
export async function markAllNotificationsAsRead(userId: string) {
  try {
    const result = await prisma.notification.updateMany({
      where: {
        userId,
        read: false,
      },
      data: {
        read: true,
      },
    });
    return result;
  } catch (error) {
    console.error('标记所有通知已读失败:', error);
    throw error;
  }
}
